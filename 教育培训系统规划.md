# 教育培训系统规划设计

## 一、业务规划

### 1.1 系统概述
教育培训系统是一个面向教育培训机构的综合性管理平台，涵盖资源管理、计划制定、在线学习、在线考试四大核心业务模块。

### 1.2 核心业务模块

#### 1.2.1 教培资源管理模块
**功能范围：**
- 课程体系库管理
- 教官库管理
- 教案库管理
- 考题库管理
- 人才库管理

**业务流程：**
1. 资源录入：支持批量导入和单个录入
2. 资源分类：按专业、级别、类型等维度分类
3. 资源审核：多级审核机制确保资源质量
4. 资源维护：版本管理、更新记录、状态管理

#### 1.2.2 教培计划编制模块
**功能范围：**
- 计划基本信息录入
- 参训人员选择
- 课程体系配置
- 计划审核流程
- 计划发布管理

**业务流程：**
1. 计划申请：总站/支队培训部门提交计划申请
2. 信息完善：录入培训目标、时间安排、资源需求等
3. 人员配置：选择参训人员和授课教官
4. 课程安排：从课程体系库选择相关课程
5. 审核流程：总站政治处多级审核
6. 计划发布：审核通过后向相关人员发布

#### 1.2.3 在线学习模块
**功能范围：**
- 教官教案配置
- 学习计划编制
- 学习任务生成
- 在线学习执行
- 学习进度跟踪

**业务流程：**
1. 教案配置：教官为课程选择和上传教案
2. 计划制定：教官编制详细的学习计划
3. 任务生成：系统自动为每个学员生成个性化学习任务
4. 在线学习：学员按任务清单进行学习
5. 进度跟踪：系统实时记录学习时长和完成情况

#### 1.2.4 在线考试模块
**功能范围：**
- 考试要求制定
- 试卷生成配置
- 在线答题系统
- 自动评分系统
- 成绩管理

**业务流程：**
1. 考试配置：主办部门设定考试要求和试卷类型
2. 试卷生成：系统根据要求从题库随机抽取试题
3. 考试执行：参训人员在线答题
4. 自动评分：系统自动计算分数并转换等级
5. 成绩发布：成绩统计和结果通知

### 1.3 用户角色定义
- **系统管理员**：系统配置、用户管理、权限分配
- **培训主办部门**：计划制定、考试配置、资源审核
- **总站政治处**：计划审核、整体监督
- **教官**：教案管理、学习计划制定
- **参训人员**：在线学习、在线考试
- **资源管理员**：教培资源维护和管理

## 二、技术路线规划

### 2.1 技术架构选型

#### 2.1.1 整体架构
采用**单体架构**，简化部署和维护：
- **前端**：Vue.js 3 + Element Plus + TypeScript
- **后端**：Spring Boot 3 单体应用
- **数据库**：PostgreSQL 15+
- **缓存**：Redis 7.0 (会话缓存)
- **文件存储**：MinIO (对象存储)

#### 2.1.2 核心技术栈
**后端技术栈：**
- Spring Boot 3.2+
- Spring Security 6 (认证授权)
- Spring Data JPA (数据访问)
- PostgreSQL 15+ (主数据库)
- Redis 7.0 (会话缓存)
- MinIO (文件存储)
- Knife4j (API文档)
- Jackson (JSON处理)

**前端技术栈：**
- Vue.js 3.4+
- TypeScript 5.0+
- Element Plus (UI组件库)
- Vue Router 4 (路由管理)
- Pinia (状态管理)
- Axios (HTTP客户端)
- Vite (构建工具)

**数据存储：**
- PostgreSQL 15+ (主数据库)
- Redis 7.0 (会话缓存)
- MinIO (文件存储)

### 2.2 系统架构设计

#### 2.2.1 模块划分
采用分层架构，按业务功能划分模块：
1. **用户认证模块** (auth)
2. **资源管理模块** (resource)
3. **计划管理模块** (plan)
4. **学习管理模块** (learning)
5. **考试管理模块** (exam)
6. **通知管理模块** (notification)
7. **文件管理模块** (file)
8. **系统管理模块** (system)

#### 2.2.2 数据库设计原则
- 单一PostgreSQL数据库
- 按业务模块划分Schema
- 合理使用索引提升性能
- 使用事务保证数据一致性
- Redis缓存热点数据

### 2.3 开发计划

#### 2.3.1 第一阶段（基础框架搭建）
**时间：3-4周**
- 搭建Spring Boot单体应用架构
- 配置PostgreSQL和Redis连接
- 实现用户认证授权系统
- 完成基础数据模型设计
- 搭建前端基础框架

#### 2.3.2 第二阶段（核心业务开发）
**时间：6-8周**
- 教培资源管理模块
- 教培计划编制模块
- 文件上传和管理功能
- 基础的用户管理和权限控制

#### 2.3.3 第三阶段（学习考试模块）
**时间：4-6周**
- 在线学习模块
- 在线考试模块
- 学习进度跟踪和统计

#### 2.3.4 第四阶段（优化完善）
**时间：3-4周**
- 性能优化
- 安全加固
- 用户体验优化
- 系统测试和部署

### 2.4 技术难点和解决方案

#### 2.4.1 大文件上传和存储
**解决方案：**
- 采用分片上传技术
- 使用MinIO进行文件存储
- 实现断点续传功能
- CDN加速文件访问

#### 2.4.2 在线学习进度跟踪
**解决方案：**
- 前端定时上报学习进度
- 后端异步处理进度数据
- Redis缓存实时进度信息
- 数据库持久化完整记录

#### 2.4.3 考试防作弊机制
**解决方案：**
- 随机题目顺序和选项顺序
- 限时答题和自动提交
- 浏览器行为监控
- IP地址和设备指纹验证

#### 2.4.4 高并发处理
**解决方案：**
- Redis缓存热点数据
- 数据库连接池优化
- 异步处理非关键业务
- 接口限流和防重复提交

## 三、项目实施建议

### 3.1 团队配置建议
- **项目经理**：1人，负责整体协调
- **后端开发**：2-3人，负责Spring Boot应用开发
- **前端开发**：2人，负责Vue.js界面开发
- **测试工程师**：1人，负责功能和性能测试
- **运维工程师**：1人，负责部署和运维

### 3.2 质量保证措施
- 代码审查机制
- 单元测试覆盖率>80%
- 集成测试和端到端测试
- 性能测试和压力测试
- 安全测试和漏洞扫描

### 3.3 风险控制
- 技术选型风险：选择成熟稳定的技术栈
- 进度风险：合理评估开发周期，预留缓冲时间
- 质量风险：建立完善的测试体系
- 安全风险：实施多层次安全防护措施

## 四、数据库设计

### 4.1 核心数据表设计

#### 4.1.1 用户管理相关表
```sql
-- 用户表
CREATE TABLE sys_user (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    department_id BIGINT,
    status SMALLINT DEFAULT 1, -- 1-正常，0-禁用
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE sys_role (
    id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL,
    role_code VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(200),
    status SMALLINT DEFAULT 1, -- 1-正常，0-禁用
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_user_role UNIQUE (user_id, role_id)
);

-- 添加注释
COMMENT ON TABLE sys_user IS '用户表';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.password IS '密码';
COMMENT ON COLUMN sys_user.real_name IS '真实姓名';
COMMENT ON COLUMN sys_user.phone IS '手机号';
COMMENT ON COLUMN sys_user.email IS '邮箱';
COMMENT ON COLUMN sys_user.department_id IS '部门ID';
COMMENT ON COLUMN sys_user.status IS '状态：1-正常，0-禁用';

COMMENT ON TABLE sys_role IS '角色表';
COMMENT ON COLUMN sys_role.role_name IS '角色名称';
COMMENT ON COLUMN sys_role.role_code IS '角色编码';
COMMENT ON COLUMN sys_role.description IS '角色描述';
COMMENT ON COLUMN sys_role.status IS '状态：1-正常，0-禁用';

COMMENT ON TABLE sys_user_role IS '用户角色关联表';
```

#### 4.1.2 教培资源相关表
```sql
-- 课程体系表
CREATE TABLE course_system (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    course_name VARCHAR(100) NOT NULL COMMENT '课程名称',
    course_code VARCHAR(50) UNIQUE NOT NULL COMMENT '课程编码',
    category VARCHAR(50) COMMENT '课程分类',
    level VARCHAR(20) COMMENT '课程级别',
    duration INT COMMENT '课程时长(小时)',
    description TEXT COMMENT '课程描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_user_id BIGINT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 教官表
CREATE TABLE instructor (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    instructor_code VARCHAR(50) UNIQUE NOT NULL COMMENT '教官编号',
    speciality VARCHAR(100) COMMENT '专业特长',
    qualification VARCHAR(200) COMMENT '资质证书',
    experience_years INT COMMENT '教学经验年限',
    introduction TEXT COMMENT '个人简介',
    status TINYINT DEFAULT 1 COMMENT '状态：1-在职，0-离职',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 教案表
CREATE TABLE teaching_material (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    material_name VARCHAR(100) NOT NULL COMMENT '教案名称',
    course_id BIGINT NOT NULL COMMENT '关联课程ID',
    instructor_id BIGINT NOT NULL COMMENT '教官ID',
    file_url VARCHAR(500) COMMENT '教案文件URL',
    file_type VARCHAR(20) COMMENT '文件类型',
    file_size BIGINT COMMENT '文件大小',
    content_type VARCHAR(50) COMMENT '内容类型：视频、文档、PPT等',
    duration INT COMMENT '学习时长(分钟)',
    description TEXT COMMENT '教案描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 考题表
CREATE TABLE exam_question (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    question_text TEXT NOT NULL COMMENT '题目内容',
    question_type VARCHAR(20) NOT NULL COMMENT '题目类型：单选、多选、判断、填空',
    course_id BIGINT COMMENT '关联课程ID',
    difficulty VARCHAR(20) COMMENT '难度级别：简单、中等、困难',
    score INT DEFAULT 1 COMMENT '分值',
    options JSON COMMENT '选项内容(JSON格式)',
    correct_answer VARCHAR(500) COMMENT '正确答案',
    explanation TEXT COMMENT '答案解析',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    create_user_id BIGINT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.1.3 教培计划相关表
```sql
-- 教培计划表
CREATE TABLE training_plan (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plan_name VARCHAR(100) NOT NULL COMMENT '计划名称',
    plan_code VARCHAR(50) UNIQUE NOT NULL COMMENT '计划编号',
    training_objective TEXT COMMENT '培训目标',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    total_hours INT COMMENT '总学时',
    organizer_dept VARCHAR(100) COMMENT '主办部门',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态：DRAFT-草稿，PENDING-待审核，APPROVED-已审核，PUBLISHED-已发布，CANCELLED-已取消',
    approval_status VARCHAR(20) COMMENT '审核状态',
    approval_user_id BIGINT COMMENT '审核人ID',
    approval_time DATETIME COMMENT '审核时间',
    approval_comment TEXT COMMENT '审核意见',
    create_user_id BIGINT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 计划课程关联表
CREATE TABLE plan_course (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plan_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    instructor_id BIGINT COMMENT '指定教官',
    sequence_no INT COMMENT '课程顺序',
    planned_hours INT COMMENT '计划学时',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 计划参训人员表
CREATE TABLE plan_trainee (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plan_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    enrollment_status VARCHAR(20) DEFAULT 'ENROLLED' COMMENT '报名状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_plan_trainee (plan_id, user_id)
);
```

### 4.2 数据库索引设计
```sql
-- 用户表索引
CREATE INDEX idx_user_username ON sys_user(username);
CREATE INDEX idx_user_department ON sys_user(department_id);
CREATE INDEX idx_user_status ON sys_user(status);

-- 课程表索引
CREATE INDEX idx_course_category ON course_system(category);
CREATE INDEX idx_course_status ON course_system(status);
CREATE INDEX idx_course_code ON course_system(course_code);

-- 教案表索引
CREATE INDEX idx_material_course ON teaching_material(course_id);
CREATE INDEX idx_material_instructor ON teaching_material(instructor_id);
CREATE INDEX idx_material_status ON teaching_material(status);

-- 考题表索引
CREATE INDEX idx_question_course ON exam_question(course_id);
CREATE INDEX idx_question_type ON exam_question(question_type);
CREATE INDEX idx_question_difficulty ON exam_question(difficulty);

-- 培训计划表索引
CREATE INDEX idx_plan_status ON training_plan(status);
CREATE INDEX idx_plan_date ON training_plan(start_date, end_date);
CREATE INDEX idx_plan_organizer ON training_plan(organizer_dept);
```

## 五、API接口设计

### 5.1 RESTful API设计规范
- 使用HTTP动词表示操作：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- URL路径使用名词复数形式
- 统一的响应格式
- 合理的HTTP状态码使用
- API版本控制

### 5.2 统一响应格式
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 5.3 核心API接口

#### 5.3.1 用户认证相关API
```
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
POST /api/v1/auth/refresh        # 刷新Token
GET  /api/v1/auth/userinfo       # 获取当前用户信息
```

#### 5.3.2 教培资源管理API
```
# 课程管理
GET    /api/v1/courses           # 获取课程列表
POST   /api/v1/courses           # 创建课程
GET    /api/v1/courses/{id}      # 获取课程详情
PUT    /api/v1/courses/{id}      # 更新课程
DELETE /api/v1/courses/{id}      # 删除课程

# 教官管理
GET    /api/v1/instructors       # 获取教官列表
POST   /api/v1/instructors       # 创建教官
GET    /api/v1/instructors/{id}  # 获取教官详情
PUT    /api/v1/instructors/{id}  # 更新教官信息

# 教案管理
GET    /api/v1/materials         # 获取教案列表
POST   /api/v1/materials         # 上传教案
GET    /api/v1/materials/{id}    # 获取教案详情
PUT    /api/v1/materials/{id}    # 更新教案
DELETE /api/v1/materials/{id}    # 删除教案

# 考题管理
GET    /api/v1/questions         # 获取考题列表
POST   /api/v1/questions         # 创建考题
GET    /api/v1/questions/{id}    # 获取考题详情
PUT    /api/v1/questions/{id}    # 更新考题
DELETE /api/v1/questions/{id}    # 删除考题
```

#### 5.3.3 教培计划管理API
```
GET    /api/v1/plans             # 获取培训计划列表
POST   /api/v1/plans             # 创建培训计划
GET    /api/v1/plans/{id}        # 获取计划详情
PUT    /api/v1/plans/{id}        # 更新培训计划
DELETE /api/v1/plans/{id}        # 删除培训计划
POST   /api/v1/plans/{id}/submit # 提交审核
POST   /api/v1/plans/{id}/approve # 审核计划
POST   /api/v1/plans/{id}/publish # 发布计划
GET    /api/v1/plans/{id}/trainees # 获取参训人员
POST   /api/v1/plans/{id}/trainees # 添加参训人员
```