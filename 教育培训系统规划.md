## 教育培训系统业务与技术路线规划 (最终版)

### 1. 业务规划

**1.1 核心模块**

*   **教培资源维护模块**：
    *   **目标**：构建标准化、可复用的教培数据基础。
    *   **功能**：课程体系库管理、教官库管理、教案库管理、考题库管理、人才库管理。
    *   **数据支撑关系**：
        *   课程体系库、教官库 -> 编制教培计划
        *   教案库 -> 在线学习
        *   考题库 -> 在线考试

*   **教培计划编制模块**：
    *   **目标**：实现教培计划的制定、审批与发布流程。
    *   **功能**：计划信息录入、参训人员选择、课程体系选择、教培计划审核、教培计划发布。

*   **在线学习模块**：
    *   **目标**：提供参训人员在线学习、教官管理学习内容的功能。
    *   **功能**：教官学习计划编制、学习任务清单生成、在线学习、学习时长记录、学习完成标记。

*   **在线考试模块**：
    *   **目标**：实现考试的创建、执行、评分与结果统计。
    *   **功能**：考试要求编制、试卷自动生成（统一/随机）、在线答卷、自动阅卷与评分、成绩转换。

**1.2 用户角色**

*   **系统管理员**：管理用户、角色、权限、系统配置。
*   **培训主办部门（管理员/授权人员）**：编制教培计划、管理教培资源（课程体系、考题库）、编制考试要求、查看统计报告。
*   **政治处审核人员**：审核教培计划。
*   **教官**：管理教案、编制学习计划。
*   **参训人员**：查看教培计划、在线学习、在线考试、查询成绩。

### 2. 技术路线规划 (最终版)

**2.1 整体架构设计**

采用**单体应用架构，前后端分离**。前端负责用户界面和交互，后端 `FastAPI` 应用负责所有业务逻辑、数据处理和 API 接口提供。

**架构图 (Mermaid)**

```mermaid
graph TD
    A[用户] --> B(前端应用: Vue.js + Element UI)
    B --> C{API 请求}
    C --> D[Nginx 反向代理/负载均衡]
    D --> E[FastAPI 后端应用]

    E --> DB1(PostgreSQL 数据库)
    E --> DB2(Redis 缓存)
    E --> DB3(对象存储: MinIO)
    E --> MQ(消息队列: RabbitMQ/Kafka)
```

**2.2 技术选型建议 (最终版)**

*   **前端**：
    *   **框架**：`Vue.js`
    *   **UI 组件库**：`Element UI`
    *   **构建工具**：`Vite`

*   **后端**：
    *   **开发语言**：`Python`
    *   **Web 框架**：`FastAPI` (作为核心后端框架，集成所有业务逻辑)
    *   **ORM**：`SQLAlchemy` (与 `FastAPI` 结合良好，用于数据库操作)
    *   **异步任务**：`Celery` (配合 `RabbitMQ` 或 `Redis` 作为 Broker，处理长时间运行或异步任务，如试卷生成、学习时长统计)
    *   **API 网关/反向代理**：`Nginx` (作为反向代理，负责请求转发、负载均衡、SSL 终止)

*   **数据库**：
    *   **关系型数据库**：`PostgreSQL`
    *   **非关系型数据库**：`Redis` (用于缓存、排行榜、实时学习时长统计、Celery Broker)
    *   **对象存储**：`MinIO` (用于存储教案、考题中的多媒体文件)

*   **消息队列**：
    *   `RabbitMQ` 或 `Kafka` (用于异步任务队列 `Celery` 的 Broker，以及通知发送、日志收集等)

*   **其他**：
    *   **日志系统**：`ELK Stack` (Elasticsearch + Logstash + Kibana)
    *   **监控报警**：`Prometheus` + `Grafana`
    *   **容器化**：`Docker`
    *   **容器编排**：`Docker Compose` (用于本地开发环境和生产环境的部署管理)
    *   **版本控制**：`Git`

**2.3 关键技术挑战及解决方案**

*   **在线学习时长记录与防作弊**：前端定时发送心跳包，后端校验心跳包间隔；结合视频播放进度、页面活跃度判断。
*   **随机试卷生成算法**：设计合理的考题标签系统；实现基于标签的随机抽取算法，可利用 `Celery` 进行异步生成，避免阻塞主进程。
*   **高并发考试系统**：利用 `Celery` 异步处理；数据库读写分离；`Redis` 缓存；`Nginx` 负载均衡；通过增加服务器资源或部署多个实例来应对。
*   **权限管理**：采用基于角色的访问控制（RBAC）模型，在 `FastAPI` 内部实现精细化的权限校验。
*   **系统可扩展性**：代码层面强调模块化、组件化设计；数据库层面进行分库分表（如果数据量非常大）；硬件层面进行垂直扩展或水平扩展。

**2.4 实施步骤（里程碑）**

1.  **第一阶段：核心功能MVP**
    *   **目标**：实现教培资源维护（课程体系、教官、教案、考题）和教培计划编制的基础功能。
    *   **内容**：用户管理、角色权限管理；教培资源增删改查；教培计划基本信息录入、选择参训人员、课程、发布。

2.  **第二阶段：在线学习与考试基础**
    *   **目标**：实现在线学习和在线考试的核心流程。
    *   **内容**：教官编制学习计划；参训人员学习任务清单展示、在线学习、时长记录；考试要求编制、随机试卷生成、在线答题、自动评分、成绩转换。

3.  **第三阶段：优化与扩展**
    *   **目标**：提升系统性能、用户体验，并扩展高级功能。
    *   **内容**：学习时长记录防作弊优化；考试并发优化；报表统计与数据分析；通知提醒；人才库模块的独立开发与集成；移动端应用开发（如果需要）。