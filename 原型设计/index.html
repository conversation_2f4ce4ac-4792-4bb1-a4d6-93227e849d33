<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教育培训系统 - 原型设计导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            padding: 40px;
        }
        
        .nav-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: #4facfe;
        }
        
        .nav-card .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
        }
        
        .nav-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #333;
        }
        
        .nav-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .nav-card a {
            display: inline-block;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-card a:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .tech-item {
            background: #e9ecef;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>教育培训系统</h1>
            <p>原型设计导航 - 基于Vue.js 3 + Element Plus + PostgreSQL</p>
        </div>
        
        <div class="nav-grid">
            <div class="nav-card">
                <div class="icon">🔐</div>
                <h3>用户认证</h3>
                <p>用户登录、权限管理、个人信息维护等功能模块</p>
                <a href="auth/login.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">👥</div>
                <h3>用户管理</h3>
                <p>用户列表、角色管理、部门管理、权限分配等功能</p>
                <a href="user/user-list.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">📚</div>
                <h3>课程管理</h3>
                <p>课程创建、分类管理、课程信息维护等功能模块</p>
                <a href="course/course-list.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">👨‍🏫</div>
                <h3>教官管理</h3>
                <p>教官信息管理、资质认证、教学评价等功能</p>
                <a href="instructor/instructor-list.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">📄</div>
                <h3>教案管理</h3>
                <p>教案上传、版本管理、审核流程等功能模块</p>
                <a href="material/material-list.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">❓</div>
                <h3>考题管理</h3>
                <p>题目录入、题库管理、难度分级等功能</p>
                <a href="question/question-list.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">📋</div>
                <h3>培训计划</h3>
                <p>计划创建、审核流程、人员安排、进度跟踪等</p>
                <a href="plan/plan-list.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">🎓</div>
                <h3>在线学习</h3>
                <p>学习任务、进度跟踪、学习统计、笔记管理等</p>
                <a href="learning/learning-dashboard.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">📝</div>
                <h3>在线考试</h3>
                <p>考试创建、试卷生成、在线答题、成绩管理等</p>
                <a href="exam/exam-list.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">📊</div>
                <h3>数据统计</h3>
                <p>培训统计、学习分析、考试报告、系统概览等</p>
                <a href="dashboard/dashboard.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">📁</div>
                <h3>文件管理</h3>
                <p>文件上传、存储管理、下载统计等功能</p>
                <a href="file/file-list.html">查看原型</a>
            </div>
            
            <div class="nav-card">
                <div class="icon">⚙️</div>
                <h3>系统设置</h3>
                <p>系统配置、参数设置、日志管理等功能</p>
                <a href="system/system-config.html">查看原型</a>
            </div>
        </div>
        
        <div class="footer">
            <p>教育培训系统原型设计 - 基于现代化Web技术栈</p>
            <div class="tech-stack">
                <span class="tech-item">Vue.js 3</span>
                <span class="tech-item">Element Plus</span>
                <span class="tech-item">TypeScript</span>
                <span class="tech-item">Spring Boot</span>
                <span class="tech-item">PostgreSQL</span>
                <span class="tech-item">Redis</span>
                <span class="tech-item">MinIO</span>
            </div>
        </div>
    </div>
</body>
</html>
