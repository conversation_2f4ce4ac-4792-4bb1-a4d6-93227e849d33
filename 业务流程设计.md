# 教育培训系统业务流程设计

## 一、用户认证流程

### 1.1 用户登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant R as Redis
    participant D as 数据库

    U->>F: 输入用户名密码
    F->>B: POST /api/v1/auth/login
    B->>D: 验证用户信息
    D-->>B: 返回用户信息
    B->>B: 验证密码(BCrypt)
    B->>B: 生成JWT Token
    B->>R: 存储Token到Redis
    B-->>F: 返回Token和用户信息
    F->>F: 存储Token到localStorage
    F-->>U: 跳转到主页面
```

**详细步骤：**
1. 用户在登录页面输入用户名和密码
2. 前端验证输入格式，发送登录请求
3. 后端验证用户名是否存在
4. 使用BCrypt验证密码
5. 验证用户状态（是否禁用、锁定）
6. 生成JWT Token（包含用户ID、角色等信息）
7. 将Token存储到Redis（设置过期时间）
8. 记录登录日志（IP、设备信息等）
9. 返回Token和用户基本信息
10. 前端存储Token，跳转到主页面

### 1.2 权限验证流程
```mermaid
sequenceDiagram
    participant F as 前端
    participant B as 后端
    participant R as Redis
    participant D as 数据库

    F->>B: 请求API(携带Token)
    B->>B: 解析JWT Token
    B->>R: 检查Token是否有效
    alt Token有效
        B->>D: 查询用户权限
        D-->>B: 返回权限列表
        B->>B: 验证接口权限
        alt 有权限
            B->>B: 执行业务逻辑
            B-->>F: 返回业务数据
        else 无权限
            B-->>F: 返回403错误
        end
    else Token无效
        B-->>F: 返回401错误
    end
```

## 二、教培资源管理流程

### 2.1 课程管理流程

#### 2.1.1 课程创建流程
```mermaid
flowchart TD
    A[开始] --> B[填写课程基本信息]
    B --> C[选择课程分类]
    C --> D[设置课程属性]
    D --> E[上传课程封面]
    E --> F[保存草稿]
    F --> G{是否提交审核}
    G -->|是| H[提交审核]
    G -->|否| I[保存草稿状态]
    H --> J[等待审核]
    J --> K{审核结果}
    K -->|通过| L[课程启用]
    K -->|拒绝| M[返回修改]
    M --> B
    L --> N[结束]
    I --> N
```

**业务规则：**
- 课程编码必须唯一
- 课程名称不能重复
- 必须选择课程分类
- 课程时长必须大于0
- 只有审核通过的课程才能用于培训计划

#### 2.1.2 课程分类管理
```sql
-- 课程分类层级结构示例
INSERT INTO course_category (category_name, category_code, parent_id, category_level) VALUES
('技术培训', 'TECH', 0, 1),
('管理培训', 'MGMT', 0, 1),
('Java开发', 'JAVA', 1, 2),
('Python开发', 'PYTHON', 1, 2),
('项目管理', 'PM', 2, 2),
('团队管理', 'TEAM', 2, 2);
```

### 2.2 教官管理流程

#### 2.2.1 教官注册流程
```mermaid
flowchart TD
    A[开始] --> B[用户申请成为教官]
    B --> C[填写教官信息]
    C --> D[上传资质证书]
    D --> E[提交申请]
    E --> F[管理员审核]
    F --> G{审核结果}
    G -->|通过| H[创建教官档案]
    G -->|拒绝| I[通知申请人]
    H --> J[分配教官编号]
    J --> K[激活教官账户]
    K --> L[结束]
    I --> M[申请人修改信息]
    M --> C
```

**数据结构示例：**
```json
{
  "speciality": ["Java开发", "Spring框架", "微服务架构"],
  "qualification": [
    {
      "name": "高级工程师",
      "issuer": "工信部",
      "issueDate": "2020-06-01",
      "certificateNo": "GJ202006001"
    }
  ],
  "teachingAreas": ["后端开发", "系统架构", "技术管理"],
  "contactInfo": {
    "phone": "13800138000",
    "email": "<EMAIL>",
    "wechat": "instructor_wx"
  }
}
```

### 2.3 教案管理流程

#### 2.3.1 教案上传流程
```mermaid
sequenceDiagram
    participant I as 教官
    participant F as 前端
    participant B as 后端
    participant M as MinIO
    participant D as 数据库

    I->>F: 选择文件上传
    F->>B: 请求上传Token
    B->>M: 生成预签名URL
    M-->>B: 返回上传URL
    B-->>F: 返回上传信息
    F->>M: 直接上传文件
    M-->>F: 上传成功
    F->>B: 提交教案信息
    B->>D: 保存教案记录
    D-->>B: 保存成功
    B-->>F: 返回教案ID
    F-->>I: 显示上传成功
```

**文件信息结构：**
```json
{
  "fileInfo": {
    "fileName": "java_basic_chapter1.pdf",
    "originalName": "Java基础-第一章.pdf",
    "fileSize": 2048576,
    "fileType": "pdf",
    "mimeType": "application/pdf",
    "fileHash": "sha256_hash_value",
    "bucketName": "training-materials",
    "objectName": "materials/2024/01/java_basic_chapter1.pdf",
    "fileUrl": "http://minio:9000/training-materials/materials/2024/01/java_basic_chapter1.pdf"
  }
}
```

### 2.4 考题管理流程

#### 2.4.1 考题录入流程
```mermaid
flowchart TD
    A[开始] --> B[选择题目类型]
    B --> C{题目类型}
    C -->|单选题| D[录入题干和选项]
    C -->|多选题| E[录入题干和多个选项]
    C -->|判断题| F[录入题干]
    C -->|填空题| G[录入题干和答案]
    C -->|问答题| H[录入题干和参考答案]
    D --> I[设置正确答案]
    E --> I
    F --> I
    G --> I
    H --> I
    I --> J[添加解析说明]
    J --> K[设置难度和分值]
    K --> L[选择关联课程]
    L --> M[保存题目]
    M --> N{是否继续录入}
    N -->|是| B
    N -->|否| O[结束]
```

**题目数据结构示例：**
```json
{
  "questionText": "以下哪个是Java的基本数据类型？",
  "questionType": "single_choice",
  "difficulty": "easy",
  "score": 2.0,
  "options": [
    {"key": "A", "value": "String", "isCorrect": false},
    {"key": "B", "value": "int", "isCorrect": true},
    {"key": "C", "value": "List", "isCorrect": false},
    {"key": "D", "value": "Map", "isCorrect": false}
  ],
  "correctAnswer": "B",
  "answerAnalysis": "int是Java的基本数据类型之一，用于表示整数。String、List、Map都是引用类型。",
  "knowledgePoints": "Java基本数据类型",
  "tags": ["Java", "基础", "数据类型"]
}
```

## 三、教培计划编制流程

### 3.1 培训计划创建流程
```mermaid
flowchart TD
    A[开始] --> B[创建培训计划]
    B --> C[填写基本信息]
    C --> D[设置时间安排]
    D --> E[选择培训课程]
    E --> F[分配教官]
    F --> G[设置参训要求]
    G --> H[配置考试安排]
    H --> I[保存计划草稿]
    I --> J{是否提交审核}
    J -->|否| K[继续编辑]
    J -->|是| L[提交审核]
    K --> C
    L --> M[总站政治处审核]
    M --> N{审核结果}
    N -->|通过| O[计划审核通过]
    N -->|拒绝| P[返回修改]
    O --> Q[发布培训计划]
    Q --> R[通知相关人员]
    R --> S[开放报名]
    S --> T[结束]
    P --> C
```

### 3.2 计划审核流程
```mermaid
sequenceDiagram
    participant O as 主办部门
    participant S as 系统
    participant A as 审核人员
    participant N as 通知系统

    O->>S: 提交培训计划
    S->>S: 更新状态为"待审核"
    S->>N: 发送审核通知
    N->>A: 通知有新的审核任务
    A->>S: 查看计划详情
    A->>S: 提交审核意见
    alt 审核通过
        S->>S: 更新状态为"已审核"
        S->>N: 发送通过通知
        N->>O: 通知审核通过
    else 审核拒绝
        S->>S: 更新状态为"审核拒绝"
        S->>N: 发送拒绝通知
        N->>O: 通知审核拒绝及原因
    end
```

### 3.3 参训人员管理流程

#### 3.3.1 人员选择流程
```mermaid
flowchart TD
    A[开始] --> B[查看培训计划]
    B --> C{选择方式}
    C -->|手动选择| D[按部门筛选]
    C -->|批量导入| E[上传人员名单]
    D --> F[选择具体人员]
    E --> G[验证人员信息]
    F --> H[检查参训资格]
    G --> H
    H --> I{资格检查}
    I -->|通过| J[添加到参训名单]
    I -->|不通过| K[记录不符合原因]
    J --> L[发送参训通知]
    K --> M[生成异常报告]
    L --> N[等待确认]
    M --> N
    N --> O[结束]
```

## 四、在线学习流程

### 4.1 学习任务生成流程
```mermaid
sequenceDiagram
    participant S as 系统
    participant P as 培训计划
    participant C as 课程
    participant M as 教案
    participant T as 学习任务

    S->>P: 获取培训计划
    P-->>S: 返回计划信息
    S->>C: 获取计划中的课程
    C-->>S: 返回课程列表
    loop 每个课程
        S->>M: 获取课程教案
        M-->>S: 返回教案列表
        loop 每个参训人员
            S->>T: 创建学习任务
            T-->>S: 任务创建成功
        end
    end
    S->>S: 发送学习通知
```

### 4.2 学习进度跟踪流程
```mermaid
sequenceDiagram
    participant U as 学员
    participant F as 前端
    participant B as 后端
    participant R as Redis
    participant D as 数据库

    U->>F: 开始学习
    F->>B: 记录学习开始
    B->>D: 创建学习记录
    loop 学习过程中
        F->>F: 定时上报进度(每30秒)
        F->>B: 上报学习进度
        B->>R: 更新缓存进度
        B->>D: 批量更新进度记录
    end
    U->>F: 完成学习
    F->>B: 记录学习完成
    B->>D: 更新任务状态
    B->>B: 计算学习统计
```

**进度上报数据结构：**
```json
{
  "taskId": 12345,
  "sessionId": "session_uuid",
  "currentPosition": 1800, // 当前位置(秒)
  "duration": 30, // 本次学习时长(秒)
  "actionType": "progress", // start、progress、pause、resume、complete
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "screenResolution": "1920x1080",
    "browserName": "Chrome"
  },
  "timestamp": "2024-01-01T10:30:00Z"
}
```

## 五、在线考试流程

### 5.1 考试创建流程
```mermaid
flowchart TD
    A[开始] --> B[创建考试基本信息]
    B --> C[设置考试参数]
    C --> D[选择题目来源]
    D --> E{组卷方式}
    E -->|手动组卷| F[手动选择题目]
    E -->|自动组卷| G[设置抽题规则]
    F --> H[设置题目分值]
    G --> I[系统自动抽题]
    H --> J[预览试卷]
    I --> J
    J --> K{试卷检查}
    K -->|通过| L[保存试卷模板]
    K -->|不通过| M[调整题目]
    M --> F
    L --> N[发布考试]
    N --> O[通知考生]
    O --> P[结束]
```

### 5.2 试卷生成流程
```mermaid
sequenceDiagram
    participant S as 系统
    participant E as 考试配置
    participant Q as 题库
    participant P as 试卷

    S->>E: 获取考试配置
    E-->>S: 返回抽题规则
    alt 自动组卷
        S->>Q: 按规则查询题目
        Q-->>S: 返回候选题目
        S->>S: 随机抽取题目
        S->>S: 打乱题目顺序
        S->>S: 打乱选项顺序
    else 手动组卷
        S->>E: 获取指定题目
        E-->>S: 返回题目列表
    end
    S->>P: 生成个人试卷
    P-->>S: 返回试卷ID
```

### 5.3 考试执行流程
```mermaid
sequenceDiagram
    participant U as 考生
    participant F as 前端
    participant B as 后端
    participant D as 数据库

    U->>F: 进入考试页面
    F->>B: 请求开始考试
    B->>D: 生成考试试卷
    D-->>B: 返回试卷内容
    B-->>F: 返回试卷(不含答案)
    F->>F: 启动考试计时器
    F-->>U: 显示试卷

    loop 考试过程中
        U->>F: 答题
        F->>F: 本地保存答案
        F->>B: 定时提交答案(每2分钟)
        B->>D: 保存答题进度
    end

    alt 正常提交
        U->>F: 点击提交
        F->>B: 提交最终答案
    else 时间到自动提交
        F->>F: 计时器到期
        F->>B: 自动提交答案
    end

    B->>B: 自动评分
    B->>D: 保存考试结果
    B-->>F: 返回提交成功
    F-->>U: 显示提交成功页面
```

### 5.4 自动评分流程
```mermaid
flowchart TD
    A[开始评分] --> B[获取答题卡]
    B --> C[获取标准答案]
    C --> D[逐题评分]
    D --> E{题目类型}
    E -->|单选/多选/判断| F[精确匹配评分]
    E -->|填空题| G[关键词匹配评分]
    E -->|问答题| H[人工评分标记]
    F --> I[累计得分]
    G --> I
    H --> J[等待人工评分]
    I --> K[计算总分]
    K --> L[确定等级]
    L --> M[生成评分报告]
    M --> N[保存结果]
    N --> O[发送通知]
    O --> P[结束]
    J --> Q[人工评分完成]
    Q --> I
```

**评分规则示例：**
```json
{
  "gradingRules": {
    "single_choice": {
      "correctScore": 2.0,
      "wrongScore": 0.0,
      "method": "exact_match"
    },
    "multiple_choice": {
      "correctScore": 3.0,
      "partialScore": 1.5,
      "wrongScore": 0.0,
      "method": "partial_match"
    },
    "true_false": {
      "correctScore": 1.0,
      "wrongScore": 0.0,
      "method": "exact_match"
    },
    "fill_blank": {
      "correctScore": 2.0,
      "wrongScore": 0.0,
      "method": "keyword_match",
      "caseSensitive": false
    },
    "essay": {
      "maxScore": 10.0,
      "method": "manual_grading"
    }
  },
  "gradeMapping": {
    "90-100": "优秀",
    "80-89": "良好",
    "60-79": "合格",
    "0-59": "不合格"
  }
}
```

## 六、防作弊机制

### 6.1 考试监控流程
```mermaid
sequenceDiagram
    participant F as 前端
    participant M as 监控模块
    participant B as 后端
    participant A as 反作弊系统

    F->>M: 启动监控
    loop 考试期间
        M->>M: 检测页面焦点
        M->>M: 检测鼠标右键
        M->>M: 检测复制粘贴
        M->>M: 检测窗口切换
        M->>M: 检测全屏状态
        alt 发现异常行为
            M->>B: 上报异常行为
            B->>A: 记录作弊嫌疑
            A->>A: 累计违规次数
            A->>B: 返回处理建议
            alt 轻微违规
                B-->>F: 发送警告
            else 严重违规
                B-->>F: 强制提交试卷
            end
        end
    end
```

### 6.2 防作弊数据结构
```json
{
  "antiCheatData": {
    "violations": [
      {
        "type": "window_blur", // 窗口失焦
        "timestamp": "2024-01-01T10:15:30Z",
        "duration": 5000, // 持续时间(毫秒)
        "severity": "medium"
      },
      {
        "type": "copy_attempt", // 复制尝试
        "timestamp": "2024-01-01T10:20:15Z",
        "content": "部分题目内容",
        "severity": "high"
      }
    ],
    "deviceFingerprint": {
      "userAgent": "Mozilla/5.0...",
      "screenResolution": "1920x1080",
      "timezone": "Asia/Shanghai",
      "language": "zh-CN"
    },
    "behaviorAnalysis": {
      "averageAnswerTime": 45, // 平均答题时间(秒)
      "answerPattern": "sequential", // 答题模式
      "suspiciousPatterns": ["too_fast", "identical_timing"]
    }
  }
}
```

## 七、通知系统流程

### 7.1 通知发送流程
```mermaid
flowchart TD
    A[触发事件] --> B{事件类型}
    B -->|培训计划发布| C[获取参训人员]
    B -->|考试开始| D[获取考生列表]
    B -->|成绩发布| E[获取相关人员]
    B -->|审核通知| F[获取审核人员]
    C --> G[生成通知内容]
    D --> G
    E --> G
    F --> G
    G --> H[选择通知方式]
    H --> I{通知渠道}
    I -->|站内消息| J[保存到消息表]
    I -->|邮件通知| K[发送邮件]
    I -->|短信通知| L[发送短信]
    J --> M[推送到前端]
    K --> N[记录发送状态]
    L --> N
    M --> N
    N --> O[结束]
```

### 7.2 消息模板管理
```json
{
  "messageTemplates": {
    "training_plan_published": {
      "title": "培训计划发布通知",
      "content": "您好，培训计划《{planName}》已发布，请及时查看并确认参加。培训时间：{startDate} 至 {endDate}",
      "channels": ["system", "email"],
      "priority": "high"
    },
    "exam_reminder": {
      "title": "考试提醒",
      "content": "您好，考试《{examName}》将在{remainingTime}后开始，请提前做好准备。",
      "channels": ["system", "sms"],
      "priority": "urgent"
    },
    "grade_published": {
      "title": "成绩发布通知",
      "content": "您好，考试《{examName}》的成绩已发布，您的得分为{score}分，等级为{grade}。",
      "channels": ["system", "email"],
      "priority": "medium"
    }
  }
}
```

这个业务流程设计文档详细描述了教育培训系统各个模块的业务流程、数据结构和处理逻辑，为系统开发提供了完整的业务指导。
```
